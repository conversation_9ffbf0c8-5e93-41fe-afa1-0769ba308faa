import { resolve } from 'path'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import vue from '@vitejs/plugin-vue'
import mkcert from "vite-plugin-mkcert"
export default defineConfig({
  main: {
    plugins: [externalizeDepsPlugin()],
    define: {
      '__MODE__': JSON.stringify(process.env.MODE || 'prod'),
      '__NODE_ENV__': JSON.stringify(process.env.NODE_ENV || 'production')
    }
  },
  preload: {
    plugins: [externalizeDepsPlugin()]
  },
  renderer: {
    define: {
      '__APP_build_env__': JSON.stringify({
        MODE: process.env.MODE || 'prod',
        NODE_ENV: process.env.NODE_ENV || 'production',
        VITE_APP_TITLE: process.env.VITE_APP_TITLE || 'popofifi'
      })
    },
    envDir: resolve(process.cwd()), // 新增环境文件目录配置
    mode: process.env.NODE_ENV || 'production',
    resolve: {
      alias: {
        '@renderer': resolve('src/renderer/src'),
        '@': resolve('src/renderer/src')
      }
    },


    plugins: [vue(),mkcert()],
    server: {
      host: '0.0.0.0', // 允许外部访问
      port: 3000,       // 指定端口（可选）
      strictPort: true, // 严格模式（端口被占用则报错）

    }
  },

})
