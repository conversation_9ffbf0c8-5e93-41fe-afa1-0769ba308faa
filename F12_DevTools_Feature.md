# F12 开发者工具功能

## 功能说明

已为应用添加了 F12 快捷键来打开/关闭开发者工具，无论在什么环境下都可以使用。

## 主要修改

### 1. 主进程修改 (src/main/index.ts)

- **导入 globalShortcut**: 添加了 `globalShortcut` 到 electron 导入中
- **全局变量**: 添加了 `mainWindow` 全局变量来存储主窗口引用
- **启用开发者工具**: 将 `devTools` 设置为 `true`，在所有环境下都启用
- **自动打开逻辑**: 只在开发环境和测试环境下自动打开开发者工具
- **IPC 处理器**: 添加了 `toggle-devtools` IPC 处理器
- **全局快捷键**: 注册了 F12 快捷键来切换开发者工具
- **清理**: 在应用退出时清理全局快捷键

### 2. 预加载脚本修改 (src/preload/index.ts)

- **API 暴露**: 添加了 `toggleDevTools` 方法到 API 对象中

## 使用方法

### 1. 全局快捷键
- 按 **F12** 键可以在任何时候切换开发者工具的开启/关闭状态
- 无论是开发环境、测试环境还是生产环境都可以使用

### 2. 程序化调用
在渲染进程中可以通过以下方式调用：

```javascript
// 切换开发者工具
window.api.toggleDevTools()
```

## 环境行为

- **开发环境 (dev)**: 自动打开开发者工具，F12 可切换
- **测试环境 (test)**: 自动打开开发者工具，F12 可切换  
- **生产环境 (prod)**: 不自动打开，但 F12 可以打开/关闭开发者工具

## 技术实现

1. **全局快捷键注册**: 使用 `globalShortcut.register('F12', callback)` 注册快捷键
2. **开发者工具检测**: 使用 `webContents.isDevToolsOpened()` 检测当前状态
3. **开发者工具控制**: 使用 `webContents.openDevTools()` 和 `webContents.closeDevTools()` 控制
4. **IPC 通信**: 通过 IPC 机制允许渲染进程控制开发者工具

## 注意事项

- 全局快捷键在应用退出时会自动清理
- 开发者工具功能在所有环境下都可用，但只在开发和测试环境下自动打开
- F12 快捷键是全局的，即使应用失去焦点也能工作
