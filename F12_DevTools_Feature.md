# F12 开发者工具功能 - 修复版本

## 功能说明

已为应用添加了 F12 快捷键来打开/关闭开发者工具，无论在什么环境下都可以使用。

## 问题修复

### 原始问题
1. **F12 快捷键在打包后无法触发**
2. **获取不到 `__APP_build_env__` 环境变量**

### 修复方案
1. **环境变量配置修复**: 修改了 `electron.vite.config.ts` 中的环境变量定义方式
2. **全局快捷键增强**: 添加了更好的错误处理和调试日志
3. **类型声明**: 添加了全局类型声明文件

## 主要修改

### 1. 主进程修改 (src/main/index.ts)

- **导入 globalShortcut**: 添加了 `globalShortcut` 到 electron 导入中
- **全局变量**: 添加了 `mainWindow` 全局变量来存储主窗口引用
- **启用开发者工具**: 将 `devTools` 设置为 `true`，在所有环境下都启用
- **环境变量修复**: 使用 `__MODE__` 和 `__NODE_ENV__` 常量替代 `import.meta.env`
- **自动打开逻辑**: 只在开发环境和测试环境下自动打开开发者工具
- **IPC 处理器**: 添加了 `toggle-devtools` IPC 处理器，包含错误处理和返回值
- **全局快捷键**: 注册了 F12 快捷键，包含详细的错误处理和调试日志
- **清理**: 在应用退出时清理全局快捷键

### 2. 配置文件修改 (electron.vite.config.ts)

- **主进程环境变量**: 使用 `define` 配置 `__MODE__` 和 `__NODE_ENV__`
- **渲染进程环境变量**: 修复 `__APP_build_env__` 的 JSON 序列化问题

### 3. 类型声明 (src/main/global.d.ts)

- **全局常量声明**: 添加了 `__MODE__` 和 `__NODE_ENV__` 的类型声明

### 4. 预加载脚本修改 (src/preload/index.ts)

- **API 暴露**: 添加了 `toggleDevTools` 方法到 API 对象中

### 5. 调试页面 (src/renderer/src/views/debug-test.vue)

- **测试页面**: 添加了专门的调试测试页面来验证功能
- **环境变量显示**: 显示 `__APP_build_env__` 的内容
- **功能测试**: 提供按钮来测试开发者工具切换功能

## 使用方法

### 1. 全局快捷键
- 按 **F12** 键可以在任何时候切换开发者工具的开启/关闭状态
- 无论是开发环境、测试环境还是生产环境都可以使用

### 2. 程序化调用
在渲染进程中可以通过以下方式调用：

```javascript
// 切换开发者工具
window.api.toggleDevTools()
```

## 环境行为

- **开发环境 (dev)**: 自动打开开发者工具，F12 可切换
- **测试环境 (test)**: 自动打开开发者工具，F12 可切换  
- **生产环境 (prod)**: 不自动打开，但 F12 可以打开/关闭开发者工具

## 技术实现

1. **全局快捷键注册**: 使用 `globalShortcut.register('F12', callback)` 注册快捷键
2. **开发者工具检测**: 使用 `webContents.isDevToolsOpened()` 检测当前状态
3. **开发者工具控制**: 使用 `webContents.openDevTools()` 和 `webContents.closeDevTools()` 控制
4. **IPC 通信**: 通过 IPC 机制允许渲染进程控制开发者工具

## 测试方法

### 1. 访问调试页面
在应用中访问 `#/debug-test` 路由来查看调试测试页面，可以：
- 查看环境变量是否正确获取
- 测试程序化切换开发者工具
- 验证 F12 快捷键功能

### 2. 验证步骤
1. **构建应用**: `npm run build:win:test` 或 `npm run build:win:prod`
2. **安装并运行**: 安装生成的 `.exe` 文件
3. **测试 F12**: 按 F12 键应该能切换开发者工具
4. **查看日志**: 在开发者工具的控制台中查看相关日志
5. **测试环境变量**: 访问调试页面查看环境变量是否正确

## 修复的关键点

### 1. 环境变量序列化
```javascript
// 修复前 (错误)
define: {
  '__APP_build_env__': process.env
}

// 修复后 (正确)
define: {
  '__APP_build_env__': JSON.stringify({
    MODE: process.env.MODE || 'prod',
    NODE_ENV: process.env.NODE_ENV || 'production',
    VITE_APP_TITLE: process.env.VITE_APP_TITLE || 'popofifi'
  })
}
```

### 2. 主进程环境变量
```javascript
// 修复前
const buildMode = import.meta.env.MODE || 'prod';

// 修复后
const buildMode = (typeof __MODE__ !== 'undefined' ? __MODE__ : import.meta.env.MODE) || 'prod';
```

### 3. 全局快捷键错误处理
```javascript
// 添加了完整的错误处理和调试日志
try {
  const registered = globalShortcut.register('F12', callback);
  if (registered) {
    console.log('F12 global shortcut registered successfully');
  } else {
    console.error('Failed to register F12 global shortcut');
  }
} catch (error) {
  console.error('Error registering F12 shortcut:', error);
}
```

## 注意事项

- 全局快捷键在应用退出时会自动清理
- 开发者工具功能在所有环境下都可用，但只在开发和测试环境下自动打开
- F12 快捷键是全局的，即使应用失去焦点也能工作
- 环境变量现在在打包后也能正确获取
- 添加了详细的调试日志，便于排查问题
