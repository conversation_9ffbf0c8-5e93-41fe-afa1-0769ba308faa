<template>
  <div class="debug-test">
    <h1>调试测试页面</h1>
    
    <div class="section">
      <h2>环境变量测试</h2>
      <div class="info-item">
        <strong>__APP_build_env__:</strong>
        <pre>{{ JSON.stringify(buildEnv, null, 2) }}</pre>
      </div>
    </div>
    
    <div class="section">
      <h2>F12 开发者工具测试</h2>
      <button @click="toggleDevTools" class="btn">切换开发者工具 (程序化)</button>
      <p>或者直接按 <kbd>F12</kbd> 键</p>
      <div v-if="devToolsResult" class="result">
        <strong>结果:</strong> {{ JSON.stringify(devToolsResult, null, 2) }}
      </div>
    </div>
    
    <div class="section">
      <h2>快捷键说明</h2>
      <ul>
        <li><kbd>F12</kbd> - 全局快捷键切换开发者工具</li>
        <li>无论应用是否获得焦点都应该工作</li>
        <li>在所有环境下都可用（dev/test/prod）</li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DebugTest',
  data() {
    return {
      buildEnv: null,
      devToolsResult: null
    }
  },
  mounted() {
    // 获取环境变量
    try {
      this.buildEnv = typeof __APP_build_env__ !== 'undefined' ? __APP_build_env__ : 'Not available';
    } catch (error) {
      this.buildEnv = { error: error.message };
    }
  },
  methods: {
    async toggleDevTools() {
      try {
        if (window.api && window.api.toggleDevTools) {
          const result = await window.api.toggleDevTools();
          this.devToolsResult = result;
          console.log('DevTools toggle result:', result);
        } else {
          this.devToolsResult = { error: 'API not available' };
        }
      } catch (error) {
        this.devToolsResult = { error: error.message };
        console.error('Error toggling devtools:', error);
      }
    }
  }
}
</script>

<style scoped>
.debug-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  font-family: Arial, sans-serif;
}

.section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.info-item {
  margin-bottom: 15px;
}

pre {
  background: #f0f0f0;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}

.btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

.btn:hover {
  background: #0056b3;
}

.result {
  margin-top: 15px;
  padding: 10px;
  background: #e9ecef;
  border-radius: 4px;
}

kbd {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 3px;
  padding: 2px 6px;
  font-family: monospace;
  font-size: 0.9em;
}

ul {
  padding-left: 20px;
}

li {
  margin-bottom: 8px;
}
</style>
