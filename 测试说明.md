# F12 开发者工具功能测试说明

## 快速测试步骤

### 1. 构建测试版本
```bash
npm run build:win:test
```

### 2. 安装并运行
- 找到生成的 `dist/popofifi-test-1.0.19-setup.exe` 文件
- 双击安装
- 运行应用

### 3. 测试 F12 功能
1. **按 F12 键** - 应该能看到开发者工具打开/关闭
2. **查看控制台日志** - 应该能看到类似以下日志：
   ```
   F12 global shortcut registered successfully
   F12 pressed - toggling devtools
   Opening devtools (或 Closing devtools)
   ```

### 4. 测试环境变量
1. 在地址栏输入 `#/debug-test` 或通过路由导航到调试页面
2. 查看页面显示的环境变量信息
3. 点击"切换开发者工具"按钮测试程序化调用

## 预期结果

### ✅ 成功标志
- F12 键能正常切换开发者工具
- 控制台有相关调试日志
- 调试页面能显示正确的环境变量
- 程序化调用返回成功结果

### ❌ 失败标志
- F12 键无响应
- 控制台显示错误信息
- 环境变量显示为 "Not available"
- 程序化调用返回错误

## 故障排除

### 如果 F12 不工作
1. 检查控制台是否有错误日志
2. 确认全局快捷键是否注册成功
3. 检查是否有其他应用占用了 F12 快捷键

### 如果环境变量获取失败
1. 检查构建配置是否正确
2. 确认 `__APP_build_env__` 是否被正确定义
3. 查看调试页面的详细错误信息

## 构建文件位置
- 测试版本: `dist/popofifi-test-1.0.19-setup.exe`
- 生产版本: `dist/popofifi-prod-1.0.xx-setup.exe`

## 调试页面访问
- URL: `#/debug-test`
- 或者在应用中导航到调试测试页面
